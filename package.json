{"name": "doiov-ivsmp-web", "version": "1.0.0", "description": "a template project for vue3, vue-router, vuex, ViewUIPlus and vite.", "scripts": {"serve": "npm run dev", "dev": "vite serve", "build": "vite build", "build:staging": "vite build --mode=staging", "preview": "npm run build && vite preview", "preview:staging": "npm run build:staging && vite preview --mode=staging", "lint": "eslint ./src --ext .js,.vue", "lint:fix": "eslint ./src --ext .js,.vue --fix"}, "dependencies": {"axios": "^0.27.0", "dayjs": "^1.11.10", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "screenfull": "^6.0.2", "view-ui-plus": "1.3.0", "vue": "3.2.33", "vue-i18n": "^9.9.1", "vue-router": "^4.0.14", "vuex": "^4.0.2"}, "devDependencies": {"@vitejs/plugin-vue": "^2.3.1", "autoprefixer": "^10.4.5", "eslint": "^8.14.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-vue": "^8.7.1", "husky": "^7.0.4", "less": "^4.0.0", "less-loader": "^10.2.0", "mockjs": "^1.1.0", "vite": "^2.9.5", "vite-plugin-html": "^3.2.0", "vite-plugin-qiankun": "^1.0.15"}, "engines": {"node": ">= 12.0.0", "npm": ">= 6.9.0"}, "browserslist": ["> 1%", "last 2 versions"]}