import JSEncrypt from 'jsencrypt';

const publicKey =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC0Jr1NzVUQMburkZT6Rkt0eaPmH8TN6E258l2tZMJgVCP/sL4oKjroKYmNPBkSSiLKFr9wwJqfesMeef6ChGRUXjG6DX0oxQRe0f5/UnyEm/NicJwz9xwkU34gbuo1VB/EA2QZ5dl1rj9iSsiqKLK6/QFlVuzslRdAXYZC79vprwIDAQAB';

const encryptor = new JSEncrypt();
// 暴露给用户配置Rsa publickey
export function setRsaConfig(pubKey = publicKey) {
  encryptor.setPublicKey(pubKey);
}
// 返回加密后的内容
export function rsaContent(content) {
  encryptor.setPublicKey(publicKey);
  return encryptor.encrypt(content);
}
