/*
  使用示例
    mixins: [mixinsPageForm],
    data() {
      return {
        // 通用-表单
        isLoadingGetDetail: true,
        detailData: {},
        detailFormConfig: {},
        isLoadingCommitDetail: false,
        // 页面配置-API
        pageConfigAPI: {
          getDetailData: {
            apiFun: getDemoDetail,
          },
          addDetailData: {
            apiFun: addDemoItem,
            successMsg: '示例商品添加成功',
          },
          editDetailData: {
            apiFun: editDemoItem,
            successMsg: '示例商品修改成功',
          },
        },
        // 页面配置-Button
        pageConfigButton: {
          goBack: {
            routerName: 'demo-list',
          },
        },
        // 页面配置-表单
        pageItemsConfig: {},
        // 其他
        // (页面需要的其他数据，此处省略)
      };
    },

    editFormItem 的示例见 createFormItemConfig.js
    表单校验规则的示例见 createFormItemRules.js
    表单校验的一些共通的值写在 define.js 里
*/

import { createFormRules } from './createFormItemRules.js';
import { createFormItemConfig, createFormInitData, formatFormRequestData, formatFormResponseData, createDetailFormConfig } from './createFormItemConfig.js';

export default {
  computed: {
    pageTitle: function () {
      const { title } = this.getRouteMeta();
      return title;
    },
    detailFormRules: function () {
      if (this.detailFormConfig.isReadonly) {
        return {};
      }

      return createFormRules({
        pageItemsConfig: this.pageItemsConfig,
      });
    },
    formItemsConfig: function () {
      return createFormItemConfig({
        pageItemsConfig: this.pageItemsConfig,
        isReadonly: this.detailFormConfig.isReadonly,
      });
    },
  },
  methods: {
    /**
     * 返回
     */
    goBack(args = {}) {
      const { goBack = {} } = this.pageConfigButton || {};
      if (goBack.routerName) {
        this.$router.push({
          name: goBack.routerName,
          params: {
            skipConfirmLeave: args.skipConfirmLeave,
          },
        });
      }
    },
    /**
     * 打开页面
     */
    async initDetailPage(args = {}) {
      const { skipLoadingGet = false } = args;

      this.isLoadingCommitDetail = false;
      if (!skipLoadingGet) {
        this.isLoadingGetDetail = true;
      }

      const { requiredId = false } = this.getRouteMeta();
      const params = this.getRouteParams();
      const isReadonly = params.isReadonly === 'true' || params.isReadonly === true;
      const record = params.record ? JSON.parse(params.record) : {};
      if (requiredId && !record.id) {
        // 注意：因为用 id 的地方比较多，如果有些接口的字段名不是 id，推荐修改相应的 response，加上 id 属性后，继续操作
        this.$Message.warning('数据缺失');
        this.goBack({ skipConfirmLeave: true });
        return;
      }

      this.detailFormConfig = createDetailFormConfig({
        isReadonly,
        detailId: record.id,
        detailConfigAPI: this.pageConfigAPI,
      });

      if (record.id) {
        const { isErr = false, detailData = {} } = await this.getDetailData(record.id, record);

        if (isErr) {
          this.goBack({ skipConfirmLeave: true });
          return;
        }

        const initData = createFormInitData({ formItemsConfig: this.formItemsConfig, detailData, record, detailFormConfig: this.detailFormConfig });

        this.detailData = {
          ...initData,
          ...detailData,
        };
      } else {
        const { isErr = false, detailData = {} } = await this.initDetailData(record.id, record);

        if (isErr) {
          this.goBack({ skipConfirmLeave: true });
          return;
        }

        const initData = createFormInitData({ formItemsConfig: this.formItemsConfig, detailData, record, detailFormConfig: this.detailFormConfig });

        this.detailData = {
          ...initData,
          ...detailData,
        };
      }

      await this._initDetailPage();

      if (!skipLoadingGet) {
        this.isLoadingGetDetail = false;
      }
    },
    _initDetailPage() {
      return new Promise(async (resolve, reject) => {
        resolve();
      });
    },
    getRouteParams() {
      const { params = {} } = this.$route;
      return params;
    },
    getRouteMeta() {
      const { meta = {} } = this.$route;
      return meta;
    },
    getDetailData(id, record) {
      const {
        apiFun,
        successMsg,
        errMsg,
        convertRequestData = () => {},
        replactRequestData,
        convertResponseData = () => {},
        replactResponseData,
      } = this.pageConfigAPI.getDetailData || {};
      return new Promise(async (resolve, reject) => {
        if (!apiFun) {
          resolve({ detailData: record });
        }

        try {
          let requestData = { id };

          convertRequestData({ requestData });
          if (replactRequestData) {
            requestData = replactRequestData({ requestData });
          }

          const response = await apiFun(requestData);

          let responseData = response || {};
          formatFormResponseData({
            formItemsConfig: this.formItemsConfig,
            responseData,
          });
          convertResponseData({ responseData, response });
          if (replactResponseData) {
            responseData = replactResponseData({ responseData, response });
          }
          resolve({ detailData: responseData });
        } catch (error) {
          resolve({
            isErr: true,
          });
        }
      });
    },
    initDetailData(id, record) {
      const {
        apiFun,
        successMsg,
        errMsg,
        convertRequestData = () => {},
        replactRequestData,
        convertResponseData = () => {},
      } = this.pageConfigAPI.initDetailData || {};
      return new Promise(async (resolve, reject) => {
        if (!apiFun) {
          resolve({ detailData: record });
        }

        try {
          let requestData = { id };

          convertRequestData({ requestData });
          if (replactRequestData) {
            requestData = replactRequestData({ requestData });
          }

          const response = await apiFun(requestData);

          const responseData = response || {};
          formatFormResponseData({
            formItemsConfig: this.formItemsConfig,
            responseData,
          });
          convertResponseData({ responseData, response });
          resolve({ detailData: responseData });
        } catch (error) {
          resolve({
            isErr: true,
          });
        }
      });
    },
    /**
     * 获取某些下拉框所需的选项
     */
    getSelectListData({ apiFun, labelAttr = 'name', valueAttr = 'id' }) {
      return new Promise(async (resolve, reject) => {
        try {
          const requestData = {};
          const response = await apiFun(requestData);
          const listData = response || [];
          const selectList = listData.map((item) => {
            return {
              label: item[labelAttr],
              value: item[valueAttr],
            };
          });
          resolve({
            selectList,
          });
        } catch (error) {
          resolve({
            selectList: [],
          });
        }
      });
    },
    /**
     * 初始化 RadioDic 组件的值
     */
    initRadioDicValue(value, key) {
      this.pageItemsConfig[key].editFormItem.initValue = value;
    },
    /**
     * 提交
     */
    commitDetailData() {
      this.$refs.detailForm.validate((valid) => {
        if (valid) {
          let configApi = this.pageConfigAPI.addDetailData;
          if (this.detailData.id) {
            configApi = this.pageConfigAPI.editDetailData;
          }

          const { confirm = {} } = configApi;
          const { title = '提示', content = '是否确定保存当前录入的信息？', comments } = confirm;

          this.$confirmSave({
            title,
            content,
            comments,
            onOk: () => {
              this.commitDetailDataAPI();
            },
          });
        } else {
          // this.$Message.error('Fail!');
        }
      });
    },
    commitDetailDataAPI() {
      this.isLoadingCommitDetail = true;

      let requestData = this.getDetailRequestData();

      let configApi = this.pageConfigAPI.addDetailData;
      if (this.detailData.id) {
        configApi = this.pageConfigAPI.editDetailData;
        requestData.id = this.detailData.id;
      }
      const { apiFun, successMsg, errMsg, convertRequestData = () => {}, replactRequestData, convertResponseData = () => {} } = configApi;

      formatFormRequestData({
        formItemsConfig: this.formItemsConfig,
        requestData,
        $util: this.$util,
      });

      convertRequestData({ requestData });
      if (replactRequestData) {
        requestData = replactRequestData({ requestData });
      }

      apiFun(requestData)
        .then((response) => {
          const responseData = response || {};
          this.afterCommitDetailDataSuccess({ responseData });

          if (successMsg) {
            this.$Message.success(successMsg);
          }
        })
        .catch((error) => {
          // console.log(error);
          this.isLoadingCommitDetail = false;
        });
    },
    getDetailRequestData() {
      let params = {};

      Object.keys(this.formItemsConfig).forEach((key) => {
        const { prop } = this.formItemsConfig[key];
        params[prop] = this.detailData[prop];
      });

      return params;
    },
    afterCommitDetailDataSuccess() {
      this.goBack({ skipConfirmLeave: true });
    },
  },
};
