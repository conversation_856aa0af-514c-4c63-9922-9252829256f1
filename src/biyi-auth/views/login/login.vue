<template>
  <div class="biyi-login-bg">
    <div class="biyi-login-center">
      <h3 class="biyi-login-title">用户登录</h3>
      <Form ref="loginInfo" :model="loginInfo" :rules="ruleInline" @submit.prevent>
        <FormItem prop="username">
          <Input
            type="text"
            v-model="loginInfo.username"
            placeholder="请输入用户名"
            size="large"
            @on-enter="beforeLogin"
          >
            <Icon type="ios-person-outline" slot="prepend"></Icon>
          </Input>
        </FormItem>
        <FormItem prop="ppp">
          <Input
            type="password"
            v-model="loginInfo.ppp"
            placeholder="请输入密码"
            size="large"
            @on-enter="beforeLogin"
          >
            <Icon type="ios-lock-outline" slot="prepend"></Icon>
          </Input>
        </FormItem>
        <FormItem>
          <Row justify="center">
            <biyi-captcha
              :type="captchaType"
              ref="verificationCode"
              :email="currentEmail"
              v-model="showCaptcha"
              :baseUrl="baseUrl"
              @getVerifyRes="getVerifyRes"
            />
          </Row>
          <Button long type="primary" @click="beforeLogin">登录</Button>
        </FormItem>
      </Form>

      <div class="login-footer">
        <span style="float: right">
          <a href="/forget-pssword">忘记密码</a>
          <a href="/register" class="register-btn">注册</a>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { userStore } from '../store';
import { getLogin, isDoubleAuthentication } from '../../api/account';
import biyiCaptcha from '@/biyi-captcha/src/index.js';

import { regUserName } from '../../libs/reg';
import { rsaContent } from '../../libs/rsa';

export default {
  name: 'loginPage',
  components: {
    biyiCaptcha,
  },
  data() {
    const validateUserName = (rule, value, callback) => {
      const reg = regUserName;
      if (!reg.test(value)) {
        callback(new Error('账号仅能由数字和字母以及_组成，且最少四位'));
      }
      callback();
    };
    return {
      loginInfo: {
        captcha: '',
        username: '',
        ppp: '',
      },
      currentEmail: '',
      captchaType: 'dragCaptcha',
      showCaptcha: false,
      ruleInline: {
        username: [
          { required: true, message: '用户名不能为空', trigger: 'blur' },
          { validator: validateUserName, trigger: 'blur' },
        ],
        ppp: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
      },
    };
  },
  computed: {
    baseUrl() {
      return window.$biyiAuthAPIUrl;
      // return this.$biyiSetting.authAPIUrl; // [MEMO VU3升级] 原版比翼框架是在安装的时候设置了 Vue.prototype.$biyiSetting 变量
    },
  },
  methods: {
    beforeLogin() {
      this.$refs.loginInfo.validate((valid) => {
        if (valid) {
          isDoubleAuthentication({ account: this.loginInfo.username }).then((res) => {
            if (res !== 'CAPTCHA') {
              this.captchaType = 'emailCaptcha';
              this.currentEmail = res;
              this.showCaptcha = true;
            } else {
              this.captchaType = 'dragCaptcha';
              this.showCaptcha = true;
            }
          });
        } else {
          this.$Message.info('请输入用户名密码');
        }
      });
    },
    getVerifyRes(info) {
      this.loginHttp({
        biyiCaptcha: info.value,
        biyiCaptchaKey: info.key,
      });
    },
    // 登录接口获取token
    loginHttp(config) {
      const _data = {
        data: {
          password: rsaContent(this.loginInfo.ppp),
          username: this.loginInfo.username,
        },
        headers: config,
      };
      getLogin(_data)
        .then((res) => {
          // arr[0]是处理之后的菜单信息，arr[1]是用户信息，res包含token信息
          const obj = {
            1: {
              message: '首次登录，请修改密码！',
            },
            2: {
              message: '密码过期，请修改密码！',
            },
            3: {
              message: '管理员重置，请修改密码！',
            },
          };
          if (obj[res.token]) {
            this.$Message.error(obj[res.token].message);
            this.$router.push({ name: 'update-password', params: { type: res.token } }); // [MEMO VU3升级] 我们的代码里面没有注册这个路由，所以这次也先不搬运相关的代码
            return;
          }
          if (res.token === '4') {
            this.$router.push('/user-locked'); // [MEMO VU3升级] 我们的代码里面没有注册这个路由，所以这次也先不搬运相关的代码
            return;
          }
          userStore.set({
            token: res,
          });

          console.log('🎉 Login successful, triggering account login...');
          // 登录成功后，触发主应用的登录流程来获取用户信息和菜单
          this.$store.dispatch('admin/account/login').then(() => {
            console.log('✅ Account login completed');
            this.$router.replace(this.$route.query.redirect || '/');
          }).catch((error) => {
            console.error('❌ Account login failed:', error);
            this.$router.replace(this.$route.query.redirect || '/');
          });
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.biyi-login {
  &-center {
    width: 40%;
    max-width: 560px;
    background: #fff;
    padding: 30px;
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translate(-50%);
    margin: auto;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  }
  &-title {
    margin-bottom: 20px;
  }
  &-title::before {
    content: '';
    width: 4px;
    height: 18px;
    display: inline-block;
    vertical-align: middle;
    margin-top: -2px;
    margin-right: 10px;
    background-color: #2d8cf0;
  }
}
.remeber {
  float: right;
}
.btn {
  width: 100% !important;
}
.register-btn {
  margin-left: 30px;
}
:deep(.ivu-btn-success:hover),
:deep(.ivu-btn-success) {
  background-color: #2d8cf0;
  border-color: #2d8cf0;
}
</style>
