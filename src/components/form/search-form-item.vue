<template>
  <FormItem :label="configFormItem.label" :prop="configFormItem.prop">
    <SelectDic
      v-if="configFormItem.formItemType === 'Select' && configFormItem.dicType"
      v-model:modelValue="dataForm[configFormItem.prop]"
      :dicType="configFormItem.dicType"
      :placeholder="configFormItem.placeholder"
    />
    <Select
      v-else-if="configFormItem.formItemType === 'Select'"
      v-model="dataForm[configFormItem.prop]"
      :placeholder="configFormItem.placeholder"
      clearable
      transfer
    >
      <Option v-for="e in configFormItem.optionList" :key="e.value" :value="e.value">
        {{ e.label }}
      </Option>
    </Select>
    <Cascader
      v-else-if="configFormItem.formItemType === 'Cascader'"
      v-model="dataForm[configFormItem.prop]"
      :placeholder="configFormItem.placeholder"
      :clearable="configFormItem.clearable"
      transfer
      :data="configFormItem.cascaderData"
      :change-on-select="configFormItem.changeOnSelect"
    ></Cascader>
    <DatePicker
      v-else-if="configFormItem.formItemType === 'Date'"
      v-model="dataForm[configFormItem.prop]"
      :type="configFormItem.datePickerType"
      :format="configFormItem.datePickerFormat"
      :placeholder="configFormItem.placeholder"
      :clearable="configFormItem.clearable"
      :editable="false"
      style="width: 240px"
    />
    <DatePicker
      v-else-if="configFormItem.formItemType === 'DateRange'"
      v-model="dataForm[configFormItem.prop]"
      type="daterange"
      :placeholder="configFormItem.placeholder"
      :editable="false"
      style="width: 228px"
    />
    <DateRangeSeparate
      v-else-if="configFormItem.formItemType === 'DateRangeSeparate'"
      v-model:modelValue="dataForm[configFormItem.prop]"
      :type="configFormItem.datePickerType"
      :format="configFormItem.datePickerFormat"
      :placeholderBeginTime="configFormItem.placeholderBeginTime"
      :placeholderEndTime="configFormItem.placeholderEndTime"
      :notAllowFutureBegin="true"
      :earliest="configFormItem.datePickerEarliest"
      :rangeLimited="configFormItem.datePickerRangeLimited"
    />
    <DatePicker
      v-else-if="configFormItem.formItemType === 'DateTimeRange'"
      v-model="dataForm[configFormItem.prop]"
      type="datetimerange"
      :placeholder="configFormItem.placeholder"
      :editable="false"
      style="width: 320px"
    />
    <Input v-else v-model.trim="dataForm[configFormItem.prop]" :placeholder="configFormItem.placeholder" />
  </FormItem>
</template>
<script>
import SelectDic from '../select/select-dic.vue';
import DateRangeSeparate from '../date/date-range-separate.vue';

export default {
  components: {
    SelectDic,
    DateRangeSeparate,
  },
  props: {
    dataForm: {
      type: Object,
      required: true,
    },
    configFormItem: {
      type: Object,
      required: true,
    },
  },
};
</script>
<style scoped></style>
