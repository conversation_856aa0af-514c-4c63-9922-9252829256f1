import axios from 'axios';
import { Message, Notice } from 'view-ui-plus';
import store from '@/store';
import util from '@/libs/util';
import Setting from '@/config/setting';
import API from '@/api/config/index';
import { CODE_SOME_DEL_FAILED } from '@/define.js';
import { qiankunWindow } from 'vite-plugin-qiankun/dist/helper';

// 创建一个错误
function errorCreate(msg) {
  const err = new Error(msg);
  errorLog(err);
  throw err;
}

// 记录和显示错误
let errInstance = {};
function errorLog(err) {
  // 添加到日志
  store.dispatch('admin/log/push', {
    message: '数据请求异常',
    type: 'error',
    meta: {
      error: err,
    },
  });
  // 打印到控制台
  if (process.env.NODE_ENV === 'development') {
    util.log.error('>>>>>> Error >>>>>>');
    console.log(err);
  }
  // 显示提示，可配置使用 iView 的 $Message 还是 $Notice 组件来显示
  if (Setting.errorModalType === 'Message') {
    if (!errInstance[err.message]) {
      // 相同的错误提示消息，只显示一次
      const errMsgInstance = Message.error({
        content: err.message,
        duration: Setting.modalDuration,
      });
      errInstance[err.message] = errMsgInstance;
      setTimeout(() => {
        errInstance[err.message] = null;
      }, Setting.modalDuration * 1000);
    }
  } else if (Setting.errorModalType === 'Notice') {
    Notice.error({
      title: '提示',
      desc: err.message,
      duration: Setting.modalDuration,
    });
  }
}
const base = {
  baseURL: API.baseUrl,
  timeout: 60000, // 请求超时时间
  withCredentials: true,
};
// 创建一个 axios 实例
const service = axios.create(base);

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 在请求发送之前做一些处理
    const token = util.cookies.get('token');
    // 让每个请求携带token-- ['X-Token']为自定义key 请根据实际情况自行修改
    config.headers.Authorization = token;

    // 调试信息：打印请求详情
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 API Request:', {
        url: config.url,
        method: config.method,
        baseURL: config.baseURL,
        fullURL: `${config.baseURL}${config.url}`,
        headers: config.headers,
        data: config.data,
        params: config.params
      });
    }

    return config;
  },
  (error) => {
    // 发送失败
    console.log('❌ Request Error:', error);
    Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    // 调试信息：打印响应详情
    if (process.env.NODE_ENV === 'development') {
      console.log('📥 API Response:', {
        url: response.config.url,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data
      });
    }

    let showSuccessMsg = false;
    if (response.config && response.config.config) {
      if (response.config.config.showSuccessMsg) {
        showSuccessMsg = true;
      }
      if (response.config.config.skipResSuccessInterceptors) {
        return response;
      }
    }

    // dataAxios 是 axios 返回数据中的 data
    const dataAxios = response.data;
    // 这个状态码是和后端约定的
    const { code } = dataAxios;
    // 根据 code 进行判断
    if (code === undefined) {
      // 如果没有 code 代表这不是项目后端开发的接口
      return dataAxios;
    } else {
      if (code.length > 3) {
        return dataAxios;
      }
      // 有 code 代表这是一个后端接口 可以进行进一步的判断
      switch (code) {
        case 0:
          // [ 示例 ] code === 0 代表没有错误
          if (showSuccessMsg && dataAxios.msg) {
            Message.success(dataAxios.msg);
          }
          return dataAxios.data;
        case CODE_SOME_DEL_FAILED:
          // 部分删除失败
          Message.warning({
            // content: `${dataAxios.msg}: ${response.config.url}`,
            content: dataAxios.msg,
            duration: Setting.modalDuration,
          });
          return dataAxios;
        case 401:
          // [ 示例 ] 其它和后台约定的 code
          errorCreate(`[ code: 401 ] ${dataAxios.msg}: ${response.config.url}`);
          break;
        default:
          // 不是正确的 code
          // errorCreate(`${dataAxios.msg}: ${response.config.url}`);
          errorCreate(dataAxios.msg);
          break;
      }
    }
  },
  (error) => {
    // 调试信息：打印错误详情
    if (process.env.NODE_ENV === 'development') {
      console.log('❌ API Error:', {
        message: error.message,
        code: error.code,
        config: error.config,
        response: error.response ? {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
          headers: error.response.headers
        } : null
      });
    }

    if (error.config && error.config.config && error.config.config.skipResErrInterceptors) {
      return error;
    }

    if (error && error.response) {
      switch (error.response.status) {
        case 400:
          error.message = '请求错误';
          break;
        case 401:
          error.message = '未授权，请登录';
          if (qiankunWindow.__POWERED_BY_QIANKUN__) {
            window.$vuePrototype.$error(401);
          } else {
            util.error(401);
          }

          break;
        case 403:
          error.message = '拒绝访问';
          break;
        case 404:
          error.message = `请求地址出错: ${error.response.config.url}`;
          break;
        case 408:
          error.message = '请求超时';
          break;
        case 500:
          error.message = '服务器内部错误';
          break;
        case 501:
          error.message = '服务未实现';
          break;
        case 502:
          error.message = '网关错误';
          break;
        case 503:
          error.message = '服务不可用';
          break;
        case 504:
          error.message = '网关超时';
          break;
        case 505:
          error.message = 'HTTP版本不受支持';
          break;
        default:
          break;
      }
    } else if (error.message !== 'cancelHttp') {
      error.message = `接口错误，code: ${error.code}`;
    }
    error.message !== 'cancelHttp' && errorLog(error);
    return Promise.reject(error);
  }
);

export default service;

export function downloadFile({ url, defaultFileName, onSuccess = () => {}, onErr = () => {} }) {
  try {
    const token = util.cookies.get('token');
    fetch(`${API.baseUrl}${url}`, {
      method: 'GET',
      headers: new Headers({
        Authorization: token,
      }),
    }).then((res) => {
      let _fileName = defaultFileName;
      // 获取heads中的filename文件名
      const contentDisposition = res.headers.get('content-disposition');
      if (contentDisposition) {
        const contentDispositionAry = contentDisposition.split(';');
        if (contentDispositionAry[0] === 'attachment') {
          const fileNameEncode = contentDisposition.substring(contentDisposition.lastIndexOf('=') + 1, contentDisposition.length);
          _fileName = decodeURIComponent(fileNameEncode);
        }
      }

      res.blob().then((data) => {
        const blobUrl = window.URL.createObjectURL(data);
        const a = document.createElement('a');
        a.download = _fileName;
        a.href = blobUrl;
        a.click();

        onSuccess();
      });
    });
  } catch (error) {
    console.error('downloadResponse Error', error.message);
    onErr();
  }
}
