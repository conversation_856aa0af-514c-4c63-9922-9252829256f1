/*
  使用示例
    mixins: [mixinsPageList],
    data() {
      return {
        // 通用-搜索
        searchFormInitData: {},
        // 通用-列表
        listLoading: true,
        listConfig: {
          total: 0,
          size: 10,
          page: 1,
        },
        listData: [],
        exportListLoading: false,
        // 页面配置-API
        pageConfigAPI: {
          getList: {
            apiFun: getFlowPackage
          },
        },
        // 页面配置-Button
        pageConfigButton: {
          addListItems: {
            routerName: 'flow-package-add',
          },
        },
        // 页面配置-列表、搜索
        searchFormListOrder: ['套餐名称'],
        listColumnsOrder: ['CHECKBOX', '套餐名称', 'ACTION'],
        pageItemsConfig: {
        },
      };
    },
    created() {
      this.initSearchFormData();
    },
    mounted() {
      this.initPage();
    },

    searchFormItem 的示例见 createSearchForm.js
    listColumn 的示例见 createListColumns.js
*/
import { mapState } from 'vuex';

import { createSearchFormlist, getSearchFormData } from './createSearchForm.js';
import { createListColumns } from './createListColumns.js';
import { ceil } from '@/libs/lan';
import { CODE_SOME_DEL_FAILED } from '@/define.js';

export default {
  computed: {
    ...mapState('admin/user', ['permissionCodeDict']),
    ...mapState('admin/pageData', ['pageDataDict']),
    currentPageData: function () {
      const activeName = this.$route.name;
      if (activeName && this.pageDataDict && this.pageDataDict[activeName]) {
        return this.pageDataDict[activeName];
      }
      return {};
    },
    /**
     * 根据页面设置的 pageConfigButton 和 store 里 存储的 permissionCodeDict 生成按钮权限
     */
    pagePermission: function () {
      let pagePermission = {};
      const pageConfigButton = this.pageConfigButton || {};
      Object.keys(pageConfigButton).forEach((key) => {
        const permCode = pageConfigButton[key].permCode;
        pagePermission[key] = permCode ? this.permissionCodeDict[permCode] : false;
      });
      return pagePermission;
    },

    /**
     * 根据 pageItemsConfig 的配置，生成列表的 columns
     */
    listColumns: function () {
      const listColumns = createListColumns({
        listColumnsOrder: this.listColumnsOrder,
        pageItemsConfig: this.pageItemsConfig,
      });

      // 序号列
      const iINDEX = this.listColumnsOrder.indexOf('INDEX');
      if (iINDEX !== -1) {
        listColumns[iINDEX].indexMethod = (row, index) => {
          return row._index + 1 + this.listConfig.size * (this.listConfig.page - 1);
        };
      }

      return listColumns;
    },
    /**
     * 根据 pageItemsConfig 的配置，生成搜索的 columns
     */
    searchFormItems: function () {
      return createSearchFormlist({
        pageItemsConfig: this.pageItemsConfig,
      });
    },
    /**
     * 页面名（列表作为子页面的时候使用）
     */
    pageTitle: function () {
      const { title } = this.getRouteMeta();
      return title;
    },
  },
  methods: {
    /**
     * 打开页面
     */
    initPage() {
      if (!this.checkRequiredId()) {
        return;
      }

      const { skipRefreshPage } = this.restorePageData();
      if (!skipRefreshPage) {
        this.refreshPage();
      }
    },
    initPagePromise() {
      return new Promise(async (resolve, reject) => {
        try {
          this.initPage();
          resolve();
        } catch (error) {
          resolve();
        }
      });
    },
    checkRequiredId() {
      const { requiredId = false } = this.getRouteMeta();
      if (requiredId && !this.requiredIdValidator()) {
        this.$Message.warning('数据缺失');
        this.goBack({ skipConfirmLeave: true });
        return false;
      } else {
        return true;
      }
    },
    requiredIdValidator() {
      const params = this.getRouteParams();
      const record = params.record ? JSON.parse(params.record) : {};
      if (record.id) {
        return true;
      } else {
        return false;
      }
    },
    /**
     * 恢复页面数据（分页、搜索）。用于从详情页面返回列表页面的时候。
     */
    restorePageData() {
      let skipRefreshPage = false;

      const currentPageData = this.currentPageData || {};

      if (currentPageData.listConfig) {
        this.listConfig = currentPageData.listConfig;
      }

      if (currentPageData.searchFormData && this.$refs.searchForm) {
        skipRefreshPage = true;
        this.$refs.searchForm.setSearchFormDataAndSearch(currentPageData.searchFormData);
      }

      return {
        skipRefreshPage,
      };
    },
    /**
     * 恢复页面数据（分页、搜索）。给外部组件调用
     */
    restoreListSearch({ listConfig, searchFormData }) {
      if (listConfig) {
        this.listConfig = listConfig;
      }

      if (searchFormData) {
        this.$nextTick(() => {
          if (this.$refs.searchForm) {
            this.$refs.searchForm.setSearchFormDataAndSearch(searchFormData);
          } else {
            this.refreshPage();
          }
        });
      }
    },
    /**
     * 刷新页面数据
     */
    async refreshPage() {
      const { listData, total, responseData } = await this.getListData();
      await this.refreshListData({ listData, total });
    },
    async refreshListData({ listData, total = 0 }) {
      if (listData.length === 0 && total > 0) {
        // 当前页没有数据的话，翻到最后一页
        this.listConfig.page = ceil(total / this.listConfig.size);
        const res = await this.getListData();
        this.listData = res.listData;
        this.listConfig.total = res.total;
      } else {
        this.listData = listData;
        this.listConfig.total = total;
      }
    },

    /**
     * 设置搜索项的初始值
     */
    initSearchFormData() {
      this.searchFormInitData = {};
      this.searchFormListOrder.forEach((key) => {
        if (this.searchFormItems[key]) {
          const { prop, initValue, formItemType } = this.searchFormItems[key];
          if (initValue !== null && initValue !== undefined) {
            this.searchFormInitData[prop] = initValue;
          } else {
            if (formItemType === 'DateRangeSeparate') {
              this.searchFormInitData[prop] = {};
            }
          }
        }
      });
    },
    /**
     * 重置搜索
     */
    handleReset() {
      this.listConfig.page = 1;
      this.refreshPage();
    },

    /**
     * 搜索
     */
    handleSearch() {
      this.refreshPage();
    },

    /**
     * 获取搜索项的值
     */
    getSearchParams() {
      let searchFormData = this.searchFormInitData || {};
      if (this.$refs.searchForm) {
        searchFormData = this.$refs.searchForm.getSearchFormData();
      }
      return getSearchFormData({
        searchFormItems: this.searchFormItems,
        searchFormData,
      });
    },

    /**
     * 分页
     */
    changePage(page) {
      this.listConfig.page = page;
      this.refreshPage();
    },
    changeSize(size) {
      this.listConfig.page = 1;
      this.listConfig.size = size;
      this.refreshPage();
    },

    /**
     * 获取列表数据
     */
    getListData() {
      const {
        apiFun,
        successMsg,
        errMsg,
        convertRequestData = () => {}, // 根据页面业务更改 requestData
        replaceRequestData,
        convertResponseData = () => {}, // 根据页面业务更改 responseData
      } = this.pageConfigAPI.getList;
      return new Promise(async (resolve, reject) => {
        this.listLoading = true;

        let requestData = {
          ...this.getSearchParams(),
          pageIndex: this.listConfig.page - 1,
          size: this.listConfig.size,
        };
        convertRequestData({ requestData });
        if (replaceRequestData) {
          requestData = replaceRequestData({ requestData });
        }

        try {
          const response = await apiFun(requestData);

          const responseData = response || {};
          convertResponseData({ responseData });

          resolve({
            listData: responseData.records || [],
            total: responseData.total || 0,
            responseData,
          });
          this.listLoading = false;
        } catch (error) {
          resolve({
            listData: [],
            total: 0,
          });
          this.listLoading = false;
        }
      });
    },

    /**
     * 删除列表数据
     */
    delListItems() {
      const { confirm = {} } = this.pageConfigAPI.delListItems;
      const { title = '操作确认', content = '是否确认删除所选数据？', comments, noSelectedMsg = '请先选择要删除的内容' } = confirm;

      const selection = this.$refs.listTable.getSelection();
      if (selection.length > 0) {
        this.$confirm({
          title,
          content,
          comments,
          onOk: () => {
            this.delListItemsAPI({ selection });
          },
        });
      } else {
        this.$Message.warning(noSelectedMsg);
      }
    },
    async delListItemsAPI({ selection }) {
      const {
        apiFun,
        successMsg = '删除成功',
        errMsg,
        convertRequestData = () => {}, // 根据页面业务更改 requestData
        convertResponseData = () => {}, // 根据页面业务更改 responseData
      } = this.pageConfigAPI.delListItems;

      const requestData = {
        ids: selection.map((item) => {
          return item.id;
        }),
      };
      convertRequestData({ requestData, selection });

      try {
        const response = await apiFun(requestData);
        if (response.code !== CODE_SOME_DEL_FAILED) {
          this.$Message.success(successMsg);
        }

        this.refreshPage();
      } catch (error) {}
    },
    delListItem({ record }) {
      const { confirm = {} } = this.pageConfigAPI.delListItem;
      const { title = '提示', content = '是否确定删除？', comments } = confirm;

      this.$confirm({
        title,
        content,
        comments,
        onOk: () => {
          this.delListItemAPI({ record });
        },
      });
    },
    async delListItemAPI({ record }) {
      const {
        apiFun,
        successMsg = '删除成功',
        errMsg,
        convertRequestData = () => {}, // 根据页面业务更改 requestData
        convertResponseData = () => {}, // 根据页面业务更改 responseData
      } = this.pageConfigAPI.delListItem;

      const requestData = {
        id: record.id,
      };
      convertRequestData({ requestData, record });

      try {
        await apiFun(requestData);
        this.$Message.success(successMsg);

        this.refreshPage();
      } catch (error) {}
    },

    /**
     * 查看详情
     */
    showListItem({ record, params = {} }) {
      const { showListItem = {} } = this.pageConfigButton || {};
      if (showListItem.routerName) {
        this.$router.push({
          name: showListItem.routerName,
          params: {
            record: JSON.stringify({
              id: record.id,
            }),
            isReadonly: true,
            toChildrenPage: true,
            savePageData: this.savePageData(),
            ...params,
          },
        });
      }
    },
    savePageData() {
      let searchFormData = this.searchFormInitData || {};
      if (this.$refs.searchForm) {
        searchFormData = this.$refs.searchForm.getSearchFormData();
      }

      return JSON.stringify({
        searchFormData,
        listConfig: this.listConfig,
      });
    },

    /**
     * 新增
     */
    addListItem() {
      const { addListItems = {} } = this.pageConfigButton || {};
      if (addListItems.routerName) {
        this.$router.push({
          name: addListItems.routerName,
          params: {
            toChildrenPage: true,
            savePageData: this.savePageData(),
          },
        });
      }
    },

    /**
     * 编辑
     */
    editListItem({ record }) {
      const { editListItem = {} } = this.pageConfigButton || {};
      if (editListItem.routerName) {
        this.$router.push({
          name: editListItem.routerName,
          params: {
            record: JSON.stringify({
              id: record.id,
            }),
            toChildrenPage: true,
            savePageData: this.savePageData(),
          },
        });
      }
    },

    /**
     * 导出
     */
    exportTable() {
      this.exportListLoading = true;

      const {
        apiFun,
        successMsg,
        errMsg,
        convertRequestData = () => {}, // 根据页面业务更改 requestData
        replaceRequestData,
        convertResponseData = () => {}, // 根据页面业务更改 responseData
      } = this.pageConfigAPI.exportList;

      let requestData = {
        ...this.getSearchParams(),
      };
      convertRequestData({ requestData });
      if (replaceRequestData) {
        requestData = replaceRequestData({ requestData });
      }

      apiFun(requestData)
        .then((response) => {
          if (successMsg) {
            this.$Message.success(successMsg);
          }
          this.exportListLoading = false;
        })
        .catch((error) => {
          // console.log(error);
          this.exportListLoading = false;
        });
    },

    /**
     * 切换状态
     */
    confirmChangeStatus({ apiFun, requestData, title, content, successMsg, isRefreshPage = false }) {
      return new Promise((resolve) => {
        this.$Modal.confirm({
          title,
          content,
          onOk: () => {
            apiFun(requestData)
              .then((response) => {
                if (successMsg) {
                  this.$Message.success(successMsg);
                }

                if (isRefreshPage) {
                  this.refreshPage();
                }

                resolve(); // 只有执行成功后才切换状态
              })
              .catch((error) => {});
          },
        });
      });
    },

    /**
     * 跳转到子页面
     */
    toRouter({ routerName, params = {}, savePageData = true }) {
      this.$router.push({
        name: routerName,
        params: {
          ...params,
          toChildrenPage: true,
          savePageData: savePageData ? this.savePageData() : null, // 从子页面跳转回来的时候，需要恢复本页面的搜索、分页的数据
        },
      });
    },
    /**
     * 获取路由参数
     */
    getRouteParams() {
      const { params = {} } = this.$route;
      return params;
    },
    getRouteMeta() {
      const { meta = {} } = this.$route;
      return meta;
    },
    /**
     * 返回
     */
    goBack(args = {}) {
      const { goBack = {} } = this.pageConfigButton || {};
      if (goBack.routerName) {
        this.$router.push({
          name: goBack.routerName,
          params: {
            skipConfirmLeave: args.skipConfirmLeave,
          },
        });
      }
    },

    /**
     * 打开编辑页面( Modal )
     */
    showDetailModal(args) {
      this.$refs.detailModal.showDetailModal(args);
    },
  },
};
