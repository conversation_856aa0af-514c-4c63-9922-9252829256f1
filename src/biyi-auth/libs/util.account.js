const account = {};

const access = [];

// 获取收集的权限码
account.getAccess = function() {
  return [...access]; // 返回副本
};

// 清空权限码（用于重新登录时）
account.clearAccess = function() {
  access.length = 0;
};
// permSign
account.buildMenuTree = function (data = [], sub) {
  const tree = [];
  const menus = cleanData(data, sub);
  // map映射
  const menusMap = {};
  for (let i = 0; i < menus.length; i++) {
    menusMap[menus[i].id] = menus[i];
  }
  // 遍历构建菜单树
  for (let i = 0; i < menus.length; i++) {
    if (menusMap[menus[i].parentId]) {
      if (Array.isArray(menusMap[menus[i].parentId].children)) {
        menusMap[menus[i].parentId].children.push(menus[i]);
      } else {
        menusMap[menus[i].parentId].children = [menus[i]];
      }
    } else {
      tree.push(menus[i]);
    }
  }
  return handleMenus(tree, sub);
};

// // 子应用单独调用时处理菜单路由
// fun
// 处理菜单和用户信息数据以满足一定的格式数据
function cleanData(data, sub) {
  const menus = [];
  console.log('🔧 Processing menu data:', { data, sub });

  for (let i = 0; i < data.length; i++) {
    // 修复字段映射：使用 permCode 而不是 permissionCode
    const permissionCode = data[i].permCode || data[i].permissionCode;
    if (permissionCode) {
      access.push(permissionCode);
    }

    if (data[i].permType !== 'button' && data[i].permType !== 'interface') {
      let processedPath = data[i].permPath;

      // 处理子应用路径
      if (sub && processedPath.startsWith(`/${sub}`)) {
        processedPath = processedPath.replace(`/${sub}`, '');
      }
      // 如果处理后路径为空，设置为根路径
      if (!processedPath || processedPath === '/') {
        processedPath = '/';
      }

      const temp = {
        id: data[i].id,
        parentId: data[i].permParent,
        path: processedPath,
        title: data[i].permName,
        icon: data[i].permIcon,
        name: data[i].permName,
        type: data[i].permType,
        auth: [permissionCode],
        permOrder: data[i].permOrder,
        // helpUrl: data[i].helpUrl
      };

      if (data[i].permType === 'module') temp.auth = ['hidden'];

      console.log('📋 Processed menu item:', temp);
      menus.push(temp);
    }
  }
  menus.sort((a, b) => {
    return a.permOrder - b.permOrder;
  });
  return menus;
}
function initHeaderPath(child) {
  if (child.children) {
    const temp = child.children.find((el) => el.type === 'sider');
    if (temp) {
      return initHeaderPath(temp);
    }
  }
  return child.path;
}
function handleMenus(menus) {
  const header = [];
  let sider = [];

  console.log('🏗️ Building menu structure from:', menus);

  for (let i = 0; i < menus.length; i++) {
    if (menus[i].type === 'header') {
      header.push(menus[i]);
      if (Array.isArray(menus[i].children)) {
        let hideSider = true;
        for (let j = 0; j < menus[i].children.length; j++) {
          if (menus[i].children[j].type === 'sider') {
            hideSider = false;
          }
          menus[i].children[j].header = menus[i].name;
          sider.push(menus[i].children[j]);
        }
        menus[i].hideSider = hideSider;
      } else {
        console.error('存在根节点无children，id:', menus[i].id);
      }
    } else {
      console.error('存在根节点不是header类型，id:', menus[i].id);
    }
  }

  if (!header.length) {
    console.log('📝 No header menus found, using all menus as sider');
    sider = menus;
  }

  header.length &&
    header.forEach((el) => {
      if (el.children && el.children.length) {
        el.path = initHeaderPath(el.children[0]);
      }
    });

  const result = { header, sider };
  console.log('🎯 Final menu structure:', result);

  return result;
}

export default account;
