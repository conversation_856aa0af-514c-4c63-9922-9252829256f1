// cas服务地址
$casUrl = 'https://biyi-template-cas.ctbiyi.cn';

// 文件地址
// $fileUrl = 'http://192.168.6.231:59018'; // 已经废弃，改成后端返回完整的url
$fileUrl = '';

// 后端接口地址
$baseUrl = 'https://203.176.95.229:9003';

// 后端接口前缀
$authAPIUrl = '/doiov-monitor-websrv'; // 本系统(电池)
$prefixAPI = '/api'; // 本系统(电池)的 api 前缀
$authAPIUrlBase = '/doiov-system-auth'; // 基座
$prefixAPIBase = '/api'; // 基座的 api 前缀

// biyi-auth后端地址
$biyiAuthAPIUrl = 'https://203.176.95.229:9003/doiov-system-auth';
// $biyiAuthAPIUrl = '/mock-service'; // 使用 mock-service 的模拟数据

