import { createApp, resolveComponent } from 'vue';
import ViewUIPlus from 'view-ui-plus';
import { renderWithQiankun, qiankunWindow } from 'vite-plugin-qiankun/dist/helper';
import $router from '@/router';

import * as echarts from 'echarts';

import App from './App.vue';
import router from './router';
import store from './store';
import i18n from './i18n';
import { isEqual } from './libs/lan';

import './styles/index.less';
// import './mock'

// 自定义指令
import plugins from '@/plugins';

// 自定义组件
import Spin from '@/components/biyi-spin/index.vue';
import iLink from '@/components/link';
import iFrame from '@/components/frame';

// 自定义
import ConfirmModal from '@/components/modal/confirm-modal-installer';
import ConfirmModalSave from '@/components/modal/confirm-modal-save-installer';
import ConfirmModalCancel from '@/components/modal/confirm-modal-cancel-installer';

// 图片
const assetsImages = import.meta.globEager('./assets/images/**');

window.$t = (key, value) => i18n.global.t(key, value);

let app = null;

function render(props = {}) {
  if (props.qiankunStore) {
    // 开启路由托管
    props.qiankunStore.commit('enableRouteProxy');
  }

  const { container } = props;
  app = createApp(App);

  app.config.globalProperties.$echarts = echarts;
  app.config.globalProperties.requireImg = (url) => {
    const path = url.replace('@', '.');
    return assetsImages[path]?.default;
    // return new URL(url.replace('@', '.'), import.meta.url).href;
    // return new URL(location.origin + '/doiov-hpc' + url.replace('@', ''), import.meta.url).href;
  };
  app.config.globalProperties.$resolveComponent = resolveComponent;

  window.$vueApp = app;
  window.$vuePrototype = app.config.globalProperties;

  app
    .use(router)
    .use(store)
    .use(i18n)
    .use(ViewUIPlus, {
      i18n,
    })
    .use(plugins)
    .use(ConfirmModal)
    .use(ConfirmModalSave)
    .use(ConfirmModalCancel)
    .component('BiyiSpin', Spin)
    .component('ILink', iLink)
    .component('IFrame', iFrame)
    .mount(container ? container.querySelector('#app') : '#app');
}

const initQianKun = () => {
  renderWithQiankun({
    mount(props) {
      render(props);
      props.onGlobalStateChange &&
        props.onGlobalStateChange((value, prev) => {
          if (!window.$vuePrototype.$error) {
            window.$vuePrototype.$error = value.error;
          }

          if (!window.$vuePrototype.$auth) {
            // 根据权限码判断是否有相关权限
            // eg ：$auth('check-userList'); 有权限返回 true，无权限返回false
            window.$vuePrototype.$auth = value.auth;
          }

          if (!isEqual(value.userData, store.state.admin.user.info)) {
            // 接收基座传的用户信息
            store.dispatch('admin/user/set', value.userData, { root: true });
          }

          if (value.qiankunRouteChange && value.qiankunRouteChange.toPath) {
            let { toPath } = value.qiankunRouteChange;
            toPath = toPath.replace(`/${process.env.APP_SYSTEM_CODE}`, '');
            if (toPath !== $router.currentRoute.value.fullPath) {
              $router.push(toPath);
            }
          }
        }, true);
    },
    bootstrap() {},
    unmount() {
      app.unmount();
    },
  });
};

// 开发环境下添加调试工具
if (process.env.NODE_ENV === 'development') {
  window.debugMenu = async () => {
    try {
      console.log('🍽️ Current Menu State:', {
        userInfo: store.state.admin.user.info,
        menuHeader: store.state.admin.menu.header,
        menuSider: store.state.admin.menu.sider,
        filterSider: store.getters['admin/menu/filterSider'],
        activePath: store.state.admin.menu.activePath,
        headerName: store.state.admin.menu.headerName
      });

      return {
        userInfo: store.state.admin.user.info,
        menuState: store.state.admin.menu,
        filterSider: store.getters['admin/menu/filterSider']
      };
    } catch (error) {
      console.error('Debug menu error:', error);
      return { error: error.message };
    }
  };

  console.log('💡 Debug tips:');
  console.log('  - Run window.debugMenu() to see menu state');
}

qiankunWindow.__POWERED_BY_QIANKUN__ ? initQianKun() : render();
