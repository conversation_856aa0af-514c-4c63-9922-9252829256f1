export function createFormRules({ pageItemsConfig }) {
  let _detailFormRules = {};

  Object.keys(pageItemsConfig).forEach((key) => {
    const config = pageItemsConfig[key];
    if (config.editFormItem && config.editFormItem.formRules) {
      const { ruleLabel, label, valuekey, ruleKey = valuekey, formRules = [] } = config.editFormItem;
      const _label = ruleLabel || label || key;

      let rules = [];
      formRules.forEach((item) => {
        createFormItemRules({
          label: _label,
          valuekey,
          config: item,
          rules,
        });
      });
      _detailFormRules[ruleKey] = rules;
    }
  });

  return _detailFormRules;
}

export function createFormItemRules({ label, valuekey, config = {}, rules }) {
  switch (config.ruleType) {
    case 'required':
      /**
       * [HELP formItemRules > { ruleType: 'required' }]
       * 校验内容：必须输入
       */
      createRuleRequired({ label, valuekey, config, rules });
      break;
    case 'email':
      /**
       * [HELP formItemRules > { ruleType: 'email' }]
       * 校验内容：邮箱格式
       */
      createRuleEmail({ label, valuekey, config, rules });
      break;
    case 'phone':
      /**
       * [HELP formItemRules > { ruleType: 'phone' }]
       * 校验内容：手机号格式
       */
      createRulePhone({ label, valuekey, config, rules });
      break;
    case 'length':
      /**
       * [HELP formItemRules > { ruleType: 'length' }]
       * 校验内容：输入的字符长度
       * 配置项：
       *    min       至少需要输入的字符长度
       *    max       最多可以输入的字符长度
       */
      createRuleLength({ label, valuekey, config, rules });
      break;
    case 'date':
      /**
       * [HELP formItemRules > { ruleType: 'date' }]
       * 校验内容：输入的日期
       * 配置项：
       *    enableDateType       允许输入的日期类型
       */
      createRuleDate({ label, valuekey, config, rules });
      break;
    case 'float':
      /**
       * [HELP formItemRules > { ruleType: 'float' }]
       * 校验内容：只允许输入数字（正数或0、2位小数）
       */
      createRuleFloat({ label, valuekey, config, rules });
      break;
    case 'integer':
      /**
       * [HELP formItemRules > { ruleType: 'integer' }]
       * 校验内容：只允许输入正整数或0
       */
      createRuleInteger({ label, valuekey, config, rules });
      break;
    case 'normalAlphabet':
      /**
       * [HELP formItemRules > { ruleType: 'normalAlphabet' }]
       * 校验内容：只允许英文字母、数字和下划线
       */
      createRuleNormalAlphabet({ label, valuekey, config, rules });
      break;
    case 'range':
      /**
       * [HELP formItemRules > { ruleType: 'range' }]
       * 校验内容：输入值的大小
       * 配置项：
       *    min       最小值
       *    max       最大值
       */
      createRuleRange({ label, valuekey, config, rules });
      break;
    case 'num':
      /**
       * [HELP formItemRules > { ruleType: 'num' }]
       * 校验内容：值的个数
       * 配置项：
       *    min       至少要选择
       *    max       最多可以选择
       */
      createRuleNum({ label, valuekey, config, rules });
      break;
    default:
      rules.push(config);
      break;
  }
}

function createRuleRequired({ label, valuekey, config, rules }) {
  const { validatorType, ruleTrigger = 'change', ruleMessage } = config;

  let _ruleMessage = ruleMessage || '此项为必填项';
  /*
  if (!ruleMessage) {
    switch (validatorType) {
      case 'SelectNumber':
      case 'SelectMultiple':
        _ruleMessage = `请选择${label}`;
        break;
      case 'InputNumber':
        _ruleMessage = `请输入${label}`;
        break;
      default:
        _ruleMessage = `${label}不能为空`;
        break;
    }
  }
  */

  switch (validatorType) {
    case 'Array':
    case 'SelectMultiple':
      rules.push({
        required: true, // 为了在 label 前显示 *，需要配置这一项
        validator: (rule, value = [], callback) => {
          if (value === null || value.length === 0 || (value.length === 1 && value[0].trim && value[0].trim() === '')) {
            callback(new Error(_ruleMessage));
          } else {
            callback();
          }
        },
        trigger: ruleTrigger,
      });
      break;
    case 'DateRange':
    case 'TimeRange':
      rules.push({
        required: true, // 为了在 label 前显示 *，需要配置这一项
        validator: (rule, value = [], callback) => {
          if (value === null || value.length < 2 || value[0] === '' || value[0] === '') {
            callback(new Error(_ruleMessage));
          } else {
            callback();
          }
        },
        trigger: ruleTrigger,
      });
      break;
    case 'Cascader':
      rules.push({
        required: true, // 为了在 label 前显示 *，需要配置这一项
        validator: (rule, value = '', callback) => {
          if (value === '' || value === null) {
            callback(new Error(_ruleMessage));
          } else {
            callback();
          }
        },
        trigger: ruleTrigger,
      });
      break;
    case 'SelectNumber':
      rules.push({
        required: true,
        message: _ruleMessage,
        trigger: ruleTrigger,
        type: 'number',
      });
      break;
    case 'InputNumber': // 用于 InputNumber 组件
      rules.push({
        required: true,
        message: _ruleMessage,
        trigger: 'blur',
        type: 'number',
      });
      break;
    case 'InputStrNumber': // 用于输入数字的 Input
      rules.push({
        required: true, // 为了在 label 前显示 *，需要配置这一项
        validator: (rule, value, callback) => {
          if (value === undefined || value === null || value === '' || (value.trim && value.trim() === '')) {
            callback(new Error(_ruleMessage));
          } else {
            callback();
          }
        },
        trigger: ruleTrigger,
      });
      break;
    case 'Date':
      rules.push({ required: true, type: 'date', message: _ruleMessage, trigger: ruleTrigger });
      break;
    default:
      rules.push({
        required: true,
        message: _ruleMessage,
        trigger: ruleTrigger,
      });
      break;
  }
}

function createRuleEmail({ label, valuekey, config, rules }) {
  const { ruleMessage, ruleTrigger = 'change' } = config;

  let _ruleMessage = ruleMessage;
  if (!_ruleMessage) {
    _ruleMessage = '请输入正确的邮箱地址';
  }

  rules.push({
    type: 'email',
    message: _ruleMessage,
    trigger: ruleTrigger,
  });
}

function createRulePhone({ label, valuekey, config, rules }) {
  const { ruleMessage, ruleTrigger = 'change' } = config;

  let _ruleMessage = ruleMessage;
  if (!_ruleMessage) {
    _ruleMessage = '请输入正确的手机号码';
  }

  rules.push({
    validator: (rule, value = '', callback) => {
      if (value === '' || value === null) {
        // 只在有输入值的时候做校验。如果需要做必须项的校验，请使用 { ruleType: 'required' }
        callback();
      }

      // 第一位： 1、第二位： 3～9、第三到第十一位只要是数字就行
      if (!/^1[3-9]\d{9}$/.test(value)) {
        callback(new Error(_ruleMessage));
      } else {
        callback();
      }
    },
    trigger: ruleTrigger,
  });
}

function createRuleLength({ label, valuekey, config, rules }) {
  const { ruleMessage, ruleTrigger = 'change', min, max } = config;

  let _ruleMessage = ruleMessage;
  if (min !== undefined && max === undefined) {
    if (!_ruleMessage) {
      _ruleMessage = `${label}至少${min}个字符`;
    }
    rules.push({
      type: 'string',
      min,
      message: _ruleMessage,
      trigger: ruleTrigger,
    });
  } else if (min === undefined && max !== undefined) {
    if (!_ruleMessage) {
      _ruleMessage = `${label}最多${max}个字符`;
    }
    rules.push({
      type: 'string',
      max,
      message: _ruleMessage,
      trigger: ruleTrigger,
    });
  } else if (min !== undefined && max !== undefined) {
    if (!_ruleMessage) {
      _ruleMessage = `${label}需要输入${min}~${max}个字符`;
    }
    rules.push({
      type: 'string',
      min,
      max,
      message: _ruleMessage,
      trigger: ruleTrigger,
    });
  }
}

function createRuleDate({ label, valuekey, config, rules }) {
  const { ruleMessage, ruleTrigger = 'change', validatorType, enableDateType, enableDateRange } = config;

  let _ruleMessage = ruleMessage;
  if (validatorType === 'DateRange') {
    rules.push({
      validator: (rule, value = [], callback) => {
        if (value === null || value.length < 2 || value[0] === '' || value[0] === '') {
          // 只在有输入值的时候做校验。如果需要做必须项的校验，请使用 { ruleType: 'required' }
          callback();
        }

        const timeBegin = value[0].valueOf();
        const timeEnd = value[1].valueOf();

        const timeNow = Date.now();
        const oneDay = 86400000;
        const timePast = timeNow - oneDay;

        const dayNum = (timeEnd - timeBegin) / oneDay + 1;

        let isErr = false;
        if (enableDateType === 'future') {
          if (timeBegin < timeNow || timeEnd < timeNow) {
            isErr = true;
            if (ruleMessage) {
              _ruleMessage = ruleMessage.enableDateType;
            } else {
              _ruleMessage = `${label}必须是将来日期`;
            }
          }

          if (!isErr && enableDateRange) {
            if (dayNum > enableDateRange) {
              isErr = true;
              if (ruleMessage) {
                _ruleMessage = ruleMessage.enableDateRange;
              } else {
                _ruleMessage = `${label}最长只能设置${enableDateRange}天`;
              }
            }
          }
        } else if (enableDateType === 'futureAndToday') {
          if (!_ruleMessage) {
            _ruleMessage = `${label}不能设置为过去日期`;
          }
          if (timeBegin < timePast || timeEnd < timePast) {
            isErr = true;
          }
        } else if (enableDateType === 'future6dayAndToday') {
          if (!_ruleMessage) {
            _ruleMessage = `${label}只能设置未来7天内的日期`;
          }
          const timeFuture6 = timeNow + oneDay * 6;

          if (timeBegin < timePast || timeEnd < timePast) {
            isErr = true;
          } else if (timeBegin > timeFuture6 || timeEnd > timeFuture6) {
            isErr = true;
          }
        } else if (enableDateType === 'pastAndToday') {
          if (!_ruleMessage) {
            _ruleMessage = `${label}不能设置为将来日期`;
          }
          if (timeBegin > timeNow || timeEnd > timeNow) {
            isErr = true;
          }
        }

        if (isErr) {
          callback(new Error(_ruleMessage));
        } else {
          callback();
        }
      },
      trigger: ruleTrigger,
    });
  }
}

function createRuleFloat({ label, valuekey, config, rules }) {
  const { ruleMessage, ruleTrigger = 'change', floatPrecision = 2 } = config;

  let _ruleMessage = ruleMessage;
  if (!_ruleMessage) {
    _ruleMessage = '请输入正确的数字（最多允许设置2位小数）';
  }

  rules.push({
    validator: (rule, value = '', callback) => {
      if (value === '' || value === null) {
        // 只在有输入值的时候做校验。如果需要做必须项的校验，请使用 { ruleType: 'required' }
        callback();
      }

      const reg = new RegExp(`^(([1-9]{1}\\d*)|(0{1}))(\\.\\d{1,${floatPrecision}})?$`);
      if (!reg.test(value)) {
        // 只允许保留两位小数
        callback(new Error(_ruleMessage));
      } else {
        callback();
      }
    },
    trigger: ruleTrigger,
  });
}

function createRuleInteger({ label, valuekey, config, rules }) {
  const { ruleMessage, ruleTrigger = 'change' } = config;

  let _ruleMessage = ruleMessage;
  if (!_ruleMessage) {
    _ruleMessage = '请输入正确的数字（0或者正整数）';
  }

  rules.push({
    validator: (rule, value = '', callback) => {
      if (value === '' || value === null) {
        // 只在有输入值的时候做校验。如果需要做必须项的校验，请使用 { ruleType: 'required' }
        callback();
      }

      if (!/^\d+$/.test(value)) {
        callback(new Error(_ruleMessage));
      } else {
        callback();
      }
    },
    trigger: ruleTrigger,
  });
}

function createRuleNormalAlphabet({ label, valuekey, config, rules }) {
  const { ruleMessage, ruleTrigger = 'change' } = config;

  let _ruleMessage = ruleMessage;
  if (!_ruleMessage) {
    _ruleMessage = '只允许英文字母、数字和下划线';
  }

  rules.push({
    validator: (rule, value = '', callback) => {
      if (value) {
        if (/^[a-zA-Z0-9_]*$/.test(value)) {
          callback();
        } else {
          callback(new Error(_ruleMessage));
        }
      } else {
        callback();
      }
    },
    trigger: ruleTrigger,
  });
}

function createRuleRange({ label, valuekey, config, rules }) {
  const { ruleMessage, ruleTrigger = 'change', min, max } = config;

  let _ruleMessage = ruleMessage;
  if (!_ruleMessage) {
    _ruleMessage = `请输入${min}-${max}范围内的数值`;
  }

  rules.push({
    validator: (rule, value = '', callback) => {
      if (value === '' || value === null) {
        // 只在有输入值的时候做校验。如果需要做必须项的校验，请使用 { ruleType: 'required' }
        callback();
      }

      if (min !== null && min !== undefined) {
        if (Number(value) < Number(min)) {
          callback(new Error(_ruleMessage));
        }
      }

      if (max !== null && max !== undefined) {
        if (Number(value) > Number(max)) {
          callback(new Error(_ruleMessage));
        }
      }

      callback();
    },
    trigger: ruleTrigger,
  });
}

function createRuleNum({ label, valuekey, config, rules }) {
  const { ruleTrigger = 'change', min, max } = config;

  rules.push({
    validator: (rule, value = [], callback) => {
      if (value === null || value.length === 0 || (value.length === 1 && value[0].trim && value[0].trim() === '')) {
        // 只在有输入值的时候做校验。如果需要做必须项的校验，请使用 { ruleType: 'required' }
        callback();
      }

      if (min !== null && min !== undefined) {
        if (value.length < Number(min)) {
          callback(new Error(config.ruleMessageMin || `最少选择${min}个${label}`));
        }
      }

      if (max !== null && max !== undefined) {
        if (value.length > Number(max)) {
          callback(new Error(config.ruleMessageMax || `最多选择${max}个${label}`));
        }
      }

      callback();
    },
    trigger: ruleTrigger,
  });
}
